from django.db import models
from customers.models import Customer


class EquipmentCategory(models.Model):
    name = models.CharField(max_length=100, verbose_name='اسم الفئة')
    description = models.TextField(blank=True, verbose_name='الوصف')
    
    class Meta:
        verbose_name = 'فئة المعدات'
        verbose_name_plural = 'فئات المعدات'
    
    def __str__(self):
        return self.name


class Equipment(models.Model):
    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('maintenance', 'تحت الصيانة'),
        ('out_of_service', 'خارج الخدمة'),
        ('retired', 'متقاعد'),
    ]
    
    CONDITION_CHOICES = [
        ('excellent', 'ممتاز'),
        ('good', 'جيد'),
        ('fair', 'مقبول'),
        ('poor', 'ضعيف'),
    ]
    
    equipment_id = models.CharField(max_length=50, unique=True, verbose_name='رقم المعدة')
    name = models.CharField(max_length=200, verbose_name='اسم المعدة')
    category = models.ForeignKey(EquipmentCategory, on_delete=models.CASCADE, verbose_name='الفئة')
    model = models.CharField(max_length=100, verbose_name='الموديل')
    manufacturer = models.CharField(max_length=100, verbose_name='الشركة المصنعة')
    serial_number = models.CharField(max_length=100, unique=True, verbose_name='الرقم التسلسلي')
    purchase_date = models.DateField(verbose_name='تاريخ الشراء')
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='سعر الشراء')
    current_value = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='القيمة الحالية')
    location = models.CharField(max_length=200, verbose_name='الموقع')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='الحالة')
    condition = models.CharField(max_length=20, choices=CONDITION_CHOICES, default='good', verbose_name='الوضع')
    owner = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name='المالك')
    warranty_expiry = models.DateField(null=True, blank=True, verbose_name='انتهاء الضمان')
    last_maintenance = models.DateField(null=True, blank=True, verbose_name='آخر صيانة')
    next_maintenance = models.DateField(null=True, blank=True, verbose_name='الصيانة القادمة')
    notes = models.TextField(blank=True, verbose_name='ملاحظات')
    created_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_date = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    
    class Meta:
        verbose_name = 'معدة'
        verbose_name_plural = 'المعدات'
        ordering = ['-created_date']
    
    def __str__(self):
        return f"{self.name} - {self.equipment_id}"
    
    @property
    def is_under_warranty(self):
        if self.warranty_expiry:
            from django.utils import timezone
            return timezone.now().date() <= self.warranty_expiry
        return False
    
    @property
    def maintenance_due(self):
        if self.next_maintenance:
            from django.utils import timezone
            return timezone.now().date() >= self.next_maintenance
        return False


class EquipmentDocument(models.Model):
    DOCUMENT_TYPES = [
        ('manual', 'دليل المستخدم'),
        ('warranty', 'ضمان'),
        ('certificate', 'شهادة'),
        ('invoice', 'فاتورة'),
        ('other', 'أخرى'),
    ]
    
    equipment = models.ForeignKey(Equipment, on_delete=models.CASCADE, related_name='documents', verbose_name='المعدة')
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES, verbose_name='نوع المستند')
    title = models.CharField(max_length=200, verbose_name='العنوان')
    file = models.FileField(upload_to='equipment_documents/', verbose_name='الملف')
    uploaded_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    
    class Meta:
        verbose_name = 'مستند معدة'
        verbose_name_plural = 'مستندات المعدات'
    
    def __str__(self):
        return f"{self.equipment.name} - {self.title}"
