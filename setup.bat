@echo off
echo ========================================
echo       ERP Workshop Setup Script
echo ========================================
echo.

echo 1. Creating virtual environment...
py -m venv venv
if %errorlevel% neq 0 (
    echo Error creating virtual environment!
    pause
    exit /b 1
)

echo 2. Activating virtual environment...
call venv\Scripts\activate.bat

echo 3. Upgrading pip...
py -m pip install --upgrade pip

echo 4. Installing requirements...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo Error installing requirements!
    pause
    exit /b 1
)

echo 5. Creating .env file...
if not exist .env (
    copy .env.example .env
    echo Please edit .env file with your database settings!
)

echo 6. Running Django checks...
py manage.py check
if %errorlevel% neq 0 (
    echo Django check failed! Please fix the issues.
    pause
    exit /b 1
)

echo 7. Making migrations...
py manage.py makemigrations

echo 8. Applying migrations...
py manage.py migrate

echo.
echo ========================================
echo         Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Edit .env file with your database settings
echo 2. Create superuser: py manage.py createsuperuser
echo 3. Run server: py manage.py runserver
echo.
pause
