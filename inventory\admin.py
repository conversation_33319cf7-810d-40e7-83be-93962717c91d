from django.contrib import admin
from .models import SparePart, StockMovement


class StockMovementInline(admin.TabularInline):
    model = StockMovement
    extra = 0
    readonly_fields = ['date']


@admin.register(SparePart)
class SparePartAdmin(admin.ModelAdmin):
    list_display = ['part_number', 'name', 'category', 'quantity_in_stock', 'minimum_stock_level', 'unit_price', 'is_low_stock']
    list_filter = ['category', 'supplier']
    search_fields = ['part_number', 'name', 'description']
    inlines = [StockMovementInline]
    
    def get_list_display(self, request):
        list_display = list(self.list_display)
        return list_display
    
    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.boolean = True
    is_low_stock.short_description = 'مخزون منخفض'
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('part_number', 'name', 'description', 'category')
        }),
        ('المخزون', {
            'fields': ('quantity_in_stock', 'minimum_stock_level')
        }),
        ('السعر والمورد', {
            'fields': ('unit_price', 'supplier')
        }),
    )


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    list_display = ['spare_part', 'movement_type', 'quantity', 'reference', 'date']
    list_filter = ['movement_type', 'date']
    search_fields = ['spare_part__name', 'spare_part__part_number', 'reference']
    readonly_fields = ['date']
    
    fieldsets = (
        ('معلومات الحركة', {
            'fields': ('spare_part', 'movement_type', 'quantity', 'reference')
        }),
        ('تفاصيل إضافية', {
            'fields': ('notes', 'date')
        }),
    )
