from rest_framework import serializers
from .models import ReportTemplate, GeneratedReport, ReportSchedule


class ReportTemplateSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = ReportTemplate
        fields = '__all__'
        read_only_fields = ['created_by', 'created_date']


class GeneratedReportSerializer(serializers.ModelSerializer):
    template_name = serializers.CharField(source='template.name', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = GeneratedReport
        fields = '__all__'
        read_only_fields = ['generated_by', 'generated_date', 'completed_date']


class ReportScheduleSerializer(serializers.ModelSerializer):
    template_name = serializers.CharField(source='template.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = ReportSchedule
        fields = '__all__'
        read_only_fields = ['created_by', 'created_date', 'last_run']
    
    def validate_recipients(self, value):
        if not isinstance(value, list):
            raise serializers.ValidationError("المستلمون يجب أن يكونوا في شكل قائمة")
        
        for email in value:
            if not isinstance(email, str) or '@' not in email:
                raise serializers.ValidationError(f"عنوان البريد الإلكتروني غير صحيح: {email}")
        
        return value
