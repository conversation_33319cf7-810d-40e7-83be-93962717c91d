# دليل تثبيت MongoDB

## 🔧 تثبيت MongoDB على Windows

### الطريقة الأولى - التحميل المباشر:

1. **اذهب إلى موقع MongoDB**:
   - https://www.mongodb.com/try/download/community

2. **اختر الإعدادات**:
   - Version: 7.0.x (Current)
   - Platform: Windows
   - Package: msi

3. **حمل وثبت**:
   - شغل ملف .msi
   - اختر "Complete" installation
   - اختر "Install MongoDB as a Service"
   - اختر "Run service as Network Service user"

### الطريقة الثانية - باستخدام Chocolatey:

```bash
# إذا كان لديك Chocolatey مثبت
choco install mongodb
```

### الطريقة الثالثة - MongoDB Atlas (Cloud):

1. اذهب إلى: https://www.mongodb.com/atlas
2. أنشئ حساب مجاني
3. أنشئ cluster مجاني
4. احصل على connection string
5. استخدمه في ملف .env

## ✅ التحقق من التثبيت

```bash
# تحقق من الإصدار
mongod --version

# تشغيل MongoDB
mongod

# في terminal آخر، اختبر الاتصال
mongo
```

## 🚀 تشغيل MongoDB

### Windows Service:
```bash
# بدء الخدمة
net start MongoDB

# إيقاف الخدمة
net stop MongoDB
```

### تشغيل يدوي:
```bash
# في مجلد MongoDB
mongod --dbpath "C:\data\db"
```

## 🔗 إعداد الاتصال

في ملف `.env`:

```env
# للتثبيت المحلي
MONGODB_URI=mongodb://localhost:27017/erp_workshop

# لـ MongoDB Atlas
MONGODB_URI=mongodb+srv://username:<EMAIL>/erp_workshop
```

## 🧪 اختبار الاتصال

بعد تشغيل MongoDB:

```bash
# اختبر النظام مع قاعدة البيانات
npm run dev

# أو أضف بيانات تجريبية
npm run seed
```

## 🆘 حل المشاكل

### خطأ "MongoDB connection failed":
1. تأكد من تشغيل MongoDB service
2. تحقق من المنفذ 27017
3. تأكد من مجلد البيانات موجود

### خطأ "Access denied":
```bash
# أنشئ مجلد البيانات
mkdir C:\data\db
```

### خطأ "Port already in use":
```bash
# تحقق من العمليات المستخدمة للمنفذ
netstat -ano | findstr :27017
```

## 🎯 بدائل سريعة

إذا كنت لا تريد تثبيت MongoDB محلياً:

1. **استخدم MongoDB Atlas** (مجاني)
2. **استخدم Docker**:
   ```bash
   docker run -d -p 27017:27017 --name mongodb mongo
   ```

3. **استخدم النظام المبسط**:
   ```bash
   .\start-simple.bat
   ```

## ✅ التأكد من نجاح التثبيت

إذا نجح التثبيت:
- ✅ `mongod --version` يعطي رقم الإصدار
- ✅ `net start MongoDB` يعمل بدون أخطاء
- ✅ `npm run seed` يعمل بنجاح
- ✅ `npm run dev` يتصل بقاعدة البيانات
