from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from .models import MaintenanceType, MaintenanceOrder
from .serializers import (
    MaintenanceTypeSerializer, 
    MaintenanceOrderSerializer, 
    MaintenanceOrderListSerializer
)


class MaintenanceTypeViewSet(viewsets.ModelViewSet):
    queryset = MaintenanceType.objects.all()
    serializer_class = MaintenanceTypeSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering = ['name']


class MaintenanceOrderViewSet(viewsets.ModelViewSet):
    queryset = MaintenanceOrder.objects.all()
    serializer_class = MaintenanceOrderSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'priority', 'maintenance_type', 'customer', 'equipment', 'assigned_technician']
    search_fields = ['order_number', 'description', 'customer__name', 'equipment__name']
    ordering_fields = ['created_date', 'scheduled_date', 'priority']
    ordering = ['-created_date']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return MaintenanceOrderListSerializer
        return MaintenanceOrderSerializer
    
    @action(detail=False, methods=['get'])
    def pending(self, request):
        """Get pending maintenance orders"""
        orders = MaintenanceOrder.objects.filter(status='pending')
        serializer = MaintenanceOrderListSerializer(orders, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def in_progress(self, request):
        """Get maintenance orders in progress"""
        orders = MaintenanceOrder.objects.filter(status='in_progress')
        serializer = MaintenanceOrderListSerializer(orders, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue maintenance orders"""
        today = timezone.now().date()
        orders = MaintenanceOrder.objects.filter(
            scheduled_date__lt=today,
            status__in=['pending', 'in_progress']
        )
        serializer = MaintenanceOrderListSerializer(orders, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get maintenance statistics"""
        total = MaintenanceOrder.objects.count()
        by_status = {}
        by_priority = {}
        
        for status_code, status_name in MaintenanceOrder.STATUS_CHOICES:
            count = MaintenanceOrder.objects.filter(status=status_code).count()
            by_status[status_code] = {'name': status_name, 'count': count}
        
        for priority_code, priority_name in MaintenanceOrder.PRIORITY_CHOICES:
            count = MaintenanceOrder.objects.filter(priority=priority_code).count()
            by_priority[priority_code] = {'name': priority_name, 'count': count}
        
        today = timezone.now().date()
        overdue_count = MaintenanceOrder.objects.filter(
            scheduled_date__lt=today,
            status__in=['pending', 'in_progress']
        ).count()
        
        return Response({
            'total': total,
            'by_status': by_status,
            'by_priority': by_priority,
            'overdue': overdue_count
        })
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Mark maintenance order as completed"""
        order = self.get_object()
        if order.status == 'completed':
            return Response({'error': 'أمر الصيانة مكتمل بالفعل'}, status=status.HTTP_400_BAD_REQUEST)
        
        order.status = 'completed'
        order.completed_date = timezone.now()
        order.save()
        
        serializer = self.get_serializer(order)
        return Response(serializer.data)
