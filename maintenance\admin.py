from django.contrib import admin
from .models import MaintenanceType, MaintenanceOrder


@admin.register(MaintenanceType)
class MaintenanceTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'estimated_hours']
    search_fields = ['name']


@admin.register(MaintenanceOrder)
class MaintenanceOrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'equipment', 'customer', 'status', 'priority', 'scheduled_date', 'assigned_technician']
    list_filter = ['status', 'priority', 'maintenance_type', 'created_date']
    search_fields = ['order_number', 'equipment__name', 'customer__name', 'description']
    readonly_fields = ['created_date']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('order_number', 'equipment', 'customer', 'maintenance_type')
        }),
        ('تفاصيل الصيانة', {
            'fields': ('description', 'status', 'priority', 'assigned_technician')
        }),
        ('التواريخ', {
            'fields': ('scheduled_date', 'completed_date', 'created_date')
        }),
        ('التكلفة', {
            'fields': ('estimated_cost', 'actual_cost')
        }),
    )
    
    def get_readonly_fields(self, request, obj=None):
        readonly_fields = list(self.readonly_fields)
        if obj and obj.status == 'completed':
            readonly_fields.extend(['equipment', 'customer', 'maintenance_type', 'scheduled_date'])
        return readonly_fields
