from rest_framework import serializers
from .models import MaintenanceType, MaintenanceOrder
from customers.serializers import CustomerListSerializer
from equipment.serializers import EquipmentListSerializer


class MaintenanceTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaintenanceType
        fields = '__all__'


class MaintenanceOrderSerializer(serializers.ModelSerializer):
    customer_details = CustomerListSerializer(source='customer', read_only=True)
    equipment_details = EquipmentListSerializer(source='equipment', read_only=True)
    maintenance_type_name = serializers.CharField(source='maintenance_type.name', read_only=True)
    assigned_technician_name = serializers.CharField(source='assigned_technician.get_full_name', read_only=True)
    
    class Meta:
        model = MaintenanceOrder
        fields = '__all__'
        read_only_fields = ['created_date']
    
    def validate_scheduled_date(self, value):
        from django.utils import timezone
        if value < timezone.now():
            raise serializers.ValidationError("تاريخ الصيانة المجدولة لا يمكن أن يكون في الماضي")
        return value
    
    def validate_estimated_cost(self, value):
        if value <= 0:
            raise serializers.ValidationError("التكلفة المقدرة يجب أن تكون أكبر من صفر")
        return value
    
    def validate(self, data):
        if data.get('status') == 'completed' and not data.get('completed_date'):
            raise serializers.ValidationError("يجب تحديد تاريخ الإكمال عند تغيير الحالة إلى مكتمل")
        return data


class MaintenanceOrderListSerializer(serializers.ModelSerializer):
    """Serializer for listing maintenance orders with minimal fields"""
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    equipment_name = serializers.CharField(source='equipment.name', read_only=True)
    maintenance_type_name = serializers.CharField(source='maintenance_type.name', read_only=True)
    
    class Meta:
        model = MaintenanceOrder
        fields = ['id', 'order_number', 'customer_name', 'equipment_name', 
                 'maintenance_type_name', 'status', 'priority', 'scheduled_date']
