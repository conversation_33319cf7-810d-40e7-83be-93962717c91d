from rest_framework import serializers
from .models import Project, ProjectTask
from customers.serializers import CustomerListSerializer


class ProjectTaskSerializer(serializers.ModelSerializer):
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    
    class Meta:
        model = ProjectTask
        fields = '__all__'
    
    def validate(self, data):
        start_date = data.get('start_date')
        due_date = data.get('due_date')
        
        if start_date and due_date and start_date > due_date:
            raise serializers.ValidationError("تاريخ البداية لا يمكن أن يكون بعد تاريخ الاستحقاق")
        
        return data


class ProjectSerializer(serializers.ModelSerializer):
    customer_details = CustomerListSerializer(source='customer', read_only=True)
    project_manager_name = serializers.CharField(source='project_manager.get_full_name', read_only=True)
    tasks = ProjectTaskSerializer(many=True, read_only=True)
    tasks_count = serializers.SerializerMethodField()
    completed_tasks_count = serializers.SerializerMethodField()
    progress_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = Project
        fields = '__all__'
    
    def get_tasks_count(self, obj):
        return obj.tasks.count()
    
    def get_completed_tasks_count(self, obj):
        return obj.tasks.filter(completed=True).count()
    
    def get_progress_percentage(self, obj):
        total_tasks = obj.tasks.count()
        if total_tasks == 0:
            return 0
        completed_tasks = obj.tasks.filter(completed=True).count()
        return round((completed_tasks / total_tasks) * 100, 2)
    
    def validate_budget(self, value):
        if value <= 0:
            raise serializers.ValidationError("الميزانية يجب أن تكون أكبر من صفر")
        return value
    
    def validate_actual_cost(self, value):
        if value < 0:
            raise serializers.ValidationError("التكلفة الفعلية لا يمكن أن تكون سالبة")
        return value
    
    def validate(self, data):
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("تاريخ البداية لا يمكن أن يكون بعد تاريخ الانتهاء")
        
        return data


class ProjectListSerializer(serializers.ModelSerializer):
    """Serializer for listing projects with minimal fields"""
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    project_manager_name = serializers.CharField(source='project_manager.get_full_name', read_only=True)
    progress_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = Project
        fields = ['id', 'name', 'customer_name', 'project_manager_name', 'status', 
                 'start_date', 'end_date', 'budget', 'actual_cost', 'progress_percentage']
    
    def get_progress_percentage(self, obj):
        total_tasks = obj.tasks.count()
        if total_tasks == 0:
            return 0
        completed_tasks = obj.tasks.filter(completed=True).count()
        return round((completed_tasks / total_tasks) * 100, 2)
