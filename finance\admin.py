from django.contrib import admin
from .models import Invoice, Payment


class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 0
    readonly_fields = ['payment_date']


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'customer', 'maintenance_order', 'issue_date', 'due_date', 'total_amount', 'status']
    list_filter = ['status', 'issue_date', 'due_date']
    search_fields = ['invoice_number', 'customer__name', 'maintenance_order__order_number']
    readonly_fields = ['issue_date']
    inlines = [PaymentInline]
    
    fieldsets = (
        ('معلومات الفاتورة', {
            'fields': ('invoice_number', 'customer', 'maintenance_order', 'status')
        }),
        ('التواريخ', {
            'fields': ('issue_date', 'due_date')
        }),
        ('المبالغ', {
            'fields': ('subtotal', 'tax_amount', 'total_amount')
        }),
    )
    
    def get_readonly_fields(self, request, obj=None):
        readonly_fields = list(self.readonly_fields)
        if obj and obj.status == 'paid':
            readonly_fields.extend(['customer', 'maintenance_order', 'subtotal', 'tax_amount', 'total_amount'])
        return readonly_fields


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['invoice', 'amount', 'payment_date', 'payment_method', 'reference_number']
    list_filter = ['payment_method', 'payment_date']
    search_fields = ['invoice__invoice_number', 'reference_number']
    
    fieldsets = (
        ('معلومات الدفع', {
            'fields': ('invoice', 'amount', 'payment_date', 'payment_method')
        }),
        ('تفاصيل إضافية', {
            'fields': ('reference_number', 'notes')
        }),
    )
