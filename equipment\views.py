from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from .models import EquipmentCategory, Equipment, EquipmentDocument
from .serializers import (
    EquipmentCategorySerializer, 
    EquipmentSerializer, 
    EquipmentListSerializer,
    EquipmentDocumentSerializer
)


class EquipmentCategoryViewSet(viewsets.ModelViewSet):
    queryset = EquipmentCategory.objects.all()
    serializer_class = EquipmentCategorySerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name']
    ordering = ['name']


class EquipmentViewSet(viewsets.ModelViewSet):
    queryset = Equipment.objects.all()
    serializer_class = EquipmentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'condition', 'category', 'owner']
    search_fields = ['equipment_id', 'name', 'serial_number', 'model', 'manufacturer']
    ordering_fields = ['name', 'created_date', 'purchase_date', 'last_maintenance']
    ordering = ['-created_date']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return EquipmentListSerializer
        return EquipmentSerializer
    
    @action(detail=False, methods=['get'])
    def maintenance_due(self, request):
        """Get equipment that needs maintenance"""
        today = timezone.now().date()
        equipment = Equipment.objects.filter(
            next_maintenance__lte=today,
            status='active'
        )
        serializer = EquipmentListSerializer(equipment, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def warranty_expiring(self, request):
        """Get equipment with warranty expiring soon (within 30 days)"""
        from datetime import timedelta
        today = timezone.now().date()
        thirty_days = today + timedelta(days=30)
        
        equipment = Equipment.objects.filter(
            warranty_expiry__gte=today,
            warranty_expiry__lte=thirty_days
        )
        serializer = EquipmentListSerializer(equipment, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get equipment statistics"""
        total = Equipment.objects.count()
        by_status = {}
        by_condition = {}
        
        for status_code, status_name in Equipment.STATUS_CHOICES:
            count = Equipment.objects.filter(status=status_code).count()
            by_status[status_code] = {'name': status_name, 'count': count}
        
        for condition_code, condition_name in Equipment.CONDITION_CHOICES:
            count = Equipment.objects.filter(condition=condition_code).count()
            by_condition[condition_code] = {'name': condition_name, 'count': count}
        
        return Response({
            'total': total,
            'by_status': by_status,
            'by_condition': by_condition,
            'maintenance_due': Equipment.objects.filter(
                next_maintenance__lte=timezone.now().date()
            ).count()
        })


class EquipmentDocumentViewSet(viewsets.ModelViewSet):
    queryset = EquipmentDocument.objects.all()
    serializer_class = EquipmentDocumentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['equipment', 'document_type']
    search_fields = ['title', 'equipment__name']
