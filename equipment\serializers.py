from rest_framework import serializers
from .models import EquipmentCategory, Equipment, EquipmentDocument
from customers.serializers import CustomerListSerializer


class EquipmentCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = EquipmentCategory
        fields = '__all__'


class EquipmentDocumentSerializer(serializers.ModelSerializer):
    class Meta:
        model = EquipmentDocument
        fields = '__all__'
        read_only_fields = ['uploaded_date']


class EquipmentSerializer(serializers.ModelSerializer):
    owner_details = CustomerListSerializer(source='owner', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    documents = EquipmentDocumentSerializer(many=True, read_only=True)
    is_under_warranty = serializers.ReadOnlyField()
    maintenance_due = serializers.ReadOnlyField()
    
    class Meta:
        model = Equipment
        fields = '__all__'
        read_only_fields = ['created_date', 'updated_date', 'is_under_warranty', 'maintenance_due']
    
    def validate_purchase_price(self, value):
        if value <= 0:
            raise serializers.ValidationError("سعر الشراء يجب أن يكون أكبر من صفر")
        return value
    
    def validate_current_value(self, value):
        if value < 0:
            raise serializers.ValidationError("القيمة الحالية لا يمكن أن تكون سالبة")
        return value


class EquipmentListSerializer(serializers.ModelSerializer):
    """Serializer for listing equipment with minimal fields"""
    owner_name = serializers.CharField(source='owner.name', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    class Meta:
        model = Equipment
        fields = ['id', 'equipment_id', 'name', 'category_name', 'manufacturer', 
                 'status', 'condition', 'owner_name', 'last_maintenance']
