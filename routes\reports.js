const express = require('express');
const { protect } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// @desc    Get all reports
// @route   GET /api/reports
// @access  Private
router.get('/', async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      message: 'Reports routes - قريباً',
      data: []
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

module.exports = router;
