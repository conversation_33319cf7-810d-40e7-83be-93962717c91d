const express = require('express');
const { protect } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// @desc    Get dashboard statistics
// @route   GET /api/dashboard
// @access  Private
router.get('/', async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      message: 'Dashboard - مرحباً بك في نظام ERP Workshop',
      data: {
        totalUsers: 0,
        totalCustomers: 0,
        totalEquipment: 0,
        totalMaintenanceOrders: 0,
        systemStatus: 'نشط',
        lastUpdate: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

module.exports = router;
