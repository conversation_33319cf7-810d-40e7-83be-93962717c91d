from rest_framework import serializers
from .models import SparePart, StockMovement


class SparePartSerializer(serializers.ModelSerializer):
    is_low_stock = serializers.ReadOnlyField()
    
    class Meta:
        model = SparePart
        fields = '__all__'
    
    def validate_unit_price(self, value):
        if value <= 0:
            raise serializers.ValidationError("سعر الوحدة يجب أن يكون أكبر من صفر")
        return value
    
    def validate_quantity_in_stock(self, value):
        if value < 0:
            raise serializers.ValidationError("كمية المخزون لا يمكن أن تكون سالبة")
        return value
    
    def validate_minimum_stock_level(self, value):
        if value < 0:
            raise serializers.ValidationError("الحد الأدنى للمخزون لا يمكن أن يكون سالباً")
        return value


class SparePartListSerializer(serializers.ModelSerializer):
    """Serializer for listing spare parts with minimal fields"""
    is_low_stock = serializers.ReadOnlyField()
    
    class Meta:
        model = SparePart
        fields = ['id', 'part_number', 'name', 'category', 'quantity_in_stock', 
                 'minimum_stock_level', 'unit_price', 'is_low_stock']


class StockMovementSerializer(serializers.ModelSerializer):
    spare_part_name = serializers.CharField(source='spare_part.name', read_only=True)
    spare_part_number = serializers.CharField(source='spare_part.part_number', read_only=True)
    
    class Meta:
        model = StockMovement
        fields = '__all__'
        read_only_fields = ['date']
    
    def validate_quantity(self, value):
        if value <= 0:
            raise serializers.ValidationError("الكمية يجب أن تكون أكبر من صفر")
        return value
    
    def validate(self, data):
        spare_part = data.get('spare_part')
        movement_type = data.get('movement_type')
        quantity = data.get('quantity')
        
        if movement_type == 'out' and spare_part:
            if spare_part.quantity_in_stock < quantity:
                raise serializers.ValidationError("الكمية المطلوبة أكبر من المتوفر في المخزون")
        
        return data
    
    def create(self, validated_data):
        movement = super().create(validated_data)
        
        # Update spare part stock
        spare_part = movement.spare_part
        if movement.movement_type == 'in':
            spare_part.quantity_in_stock += movement.quantity
        elif movement.movement_type == 'out':
            spare_part.quantity_in_stock -= movement.quantity
        elif movement.movement_type == 'adjustment':
            spare_part.quantity_in_stock = movement.quantity
        
        spare_part.save()
        return movement
