from django.contrib import admin
from .models import Customer


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ['name', 'customer_type', 'contact_person', 'phone', 'email', 'credit_limit', 'created_date']
    list_filter = ['customer_type', 'created_date']
    search_fields = ['name', 'contact_person', 'phone', 'email', 'tax_number']
    readonly_fields = ['created_date']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'customer_type', 'contact_person')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone', 'email', 'address')
        }),
        ('معلومات مالية', {
            'fields': ('tax_number', 'credit_limit')
        }),
        ('معلومات النظام', {
            'fields': ('created_date',),
            'classes': ('collapse',)
        }),
    )
