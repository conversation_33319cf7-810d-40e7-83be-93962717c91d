from django.db import models
from django.contrib.auth.models import User


class ReportTemplate(models.Model):
    REPORT_TYPES = [
        ('maintenance', 'تقرير الصيانة'),
        ('inventory', 'تقرير المخزون'),
        ('financial', 'تقرير مالي'),
        ('customer', 'تقرير العملاء'),
        ('project', 'تقرير المشاريع'),
        ('equipment', 'تقرير المعدات'),
    ]
    
    name = models.CharField(max_length=200, verbose_name='اسم القالب')
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES, verbose_name='نوع التقرير')
    description = models.TextField(verbose_name='الوصف')
    template_file = models.FileField(upload_to='report_templates/', verbose_name='ملف القالب')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_by = models.Foreign<PERSON>ey(User, on_delete=models.CASCADE, verbose_name='أنشأ بواسطة')
    created_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    class Meta:
        verbose_name = 'قالب تقرير'
        verbose_name_plural = 'قوالب التقارير'
    
    def __str__(self):
        return self.name


class GeneratedReport(models.Model):
    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('processing', 'قيد المعالجة'),
        ('completed', 'مكتمل'),
        ('failed', 'فشل'),
    ]
    
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, verbose_name='القالب')
    title = models.CharField(max_length=200, verbose_name='عنوان التقرير')
    parameters = models.JSONField(default=dict, verbose_name='المعاملات')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='الحالة')
    file = models.FileField(upload_to='generated_reports/', null=True, blank=True, verbose_name='الملف')
    generated_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشأ بواسطة')
    generated_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    completed_date = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ الإكمال')
    error_message = models.TextField(blank=True, verbose_name='رسالة الخطأ')
    
    class Meta:
        verbose_name = 'تقرير منشأ'
        verbose_name_plural = 'التقارير المنشأة'
        ordering = ['-generated_date']
    
    def __str__(self):
        return f"{self.title} - {self.generated_date.strftime('%Y-%m-%d')}"


class ReportSchedule(models.Model):
    FREQUENCY_CHOICES = [
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('quarterly', 'ربع سنوي'),
        ('yearly', 'سنوي'),
    ]
    
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, verbose_name='القالب')
    name = models.CharField(max_length=200, verbose_name='اسم الجدولة')
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, verbose_name='التكرار')
    parameters = models.JSONField(default=dict, verbose_name='المعاملات')
    recipients = models.JSONField(default=list, verbose_name='المستلمون')  # List of email addresses
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    next_run = models.DateTimeField(verbose_name='التشغيل القادم')
    last_run = models.DateTimeField(null=True, blank=True, verbose_name='آخر تشغيل')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشأ بواسطة')
    created_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    class Meta:
        verbose_name = 'جدولة تقرير'
        verbose_name_plural = 'جدولة التقارير'
    
    def __str__(self):
        return f"{self.name} - {self.frequency}"
