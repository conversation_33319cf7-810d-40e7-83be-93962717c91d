const express = require('express');
const app = express();

// Basic middleware
app.use(express.json());

// Test route
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '🎉 الخادم يعمل بنجاح!',
    timestamp: new Date().toISOString()
  });
});

// API routes
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: '🚀 مرحباً بك في نظام ERP Workshop!',
    version: '1.0.0',
    status: 'يعمل بنجاح',
    endpoints: [
      'GET /',
      'GET /api',
      'GET /api/health',
      'GET /api/test'
    ]
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    status: 'OK',
    message: '✅ النظام يعمل بشكل طبيعي',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: '🧪 اختبار ناجح!',
    data: {
      server: 'Express.js',
      node: process.version,
      platform: process.platform
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '❌ المسار غير موجود',
    requestedPath: req.originalUrl,
    availablePaths: [
      '/',
      '/api',
      '/api/health',
      '/api/test'
    ]
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log('🎉 ========================================');
  console.log('🚀    Test Server Started Successfully!');
  console.log('🎉 ========================================');
  console.log(`🌐 Server: http://localhost:${PORT}`);
  console.log(`🔍 Health: http://localhost:${PORT}/api/health`);
  console.log(`🧪 Test: http://localhost:${PORT}/api/test`);
  console.log('🎉 ========================================');
});
