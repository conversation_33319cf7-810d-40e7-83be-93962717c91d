from django.db import models
from django.contrib.auth.models import User
from customers.models import Customer

class Project(models.Model):
    STATUS_CHOICES = [
        ('planning', 'تخطيط'),
        ('active', 'نشط'),
        ('on_hold', 'معلق'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]
    
    name = models.CharField(max_length=200)
    description = models.TextField()
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planning')
    budget = models.DecimalField(max_digits=12, decimal_places=2)
    actual_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    project_manager = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
class ProjectTask(models.Model):
    project = models.Foreign<PERSON>ey(Project, on_delete=models.CASCADE, related_name='tasks')
    name = models.CharField(max_length=200)
    description = models.TextField()
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    start_date = models.DateField()
    due_date = models.DateField()
    completed = models.BooleanField(default=False)
    maintenance_order = models.ForeignKey('maintenance.MaintenanceOrder', on_delete=models.SET_NULL, null=True, blank=True)