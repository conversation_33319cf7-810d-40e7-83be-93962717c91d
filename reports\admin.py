from django.contrib import admin
from .models import ReportTemplate, GeneratedReport, ReportSchedule


@admin.register(ReportTemplate)
class ReportTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'report_type', 'is_active', 'created_by', 'created_date']
    list_filter = ['report_type', 'is_active', 'created_date']
    search_fields = ['name', 'description']
    readonly_fields = ['created_date']
    
    fieldsets = (
        ('معلومات القالب', {
            'fields': ('name', 'report_type', 'description', 'is_active')
        }),
        ('الملف', {
            'fields': ('template_file',)
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_date'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(GeneratedReport)
class GeneratedReportAdmin(admin.ModelAdmin):
    list_display = ['title', 'template', 'status', 'generated_by', 'generated_date', 'completed_date']
    list_filter = ['status', 'template__report_type', 'generated_date']
    search_fields = ['title', 'template__name']
    readonly_fields = ['generated_date', 'completed_date']
    
    fieldsets = (
        ('معلومات التقرير', {
            'fields': ('template', 'title', 'status')
        }),
        ('المعاملات', {
            'fields': ('parameters',)
        }),
        ('الملف والأخطاء', {
            'fields': ('file', 'error_message')
        }),
        ('معلومات النظام', {
            'fields': ('generated_by', 'generated_date', 'completed_date'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ReportSchedule)
class ReportScheduleAdmin(admin.ModelAdmin):
    list_display = ['name', 'template', 'frequency', 'is_active', 'next_run', 'last_run']
    list_filter = ['frequency', 'is_active', 'template__report_type']
    search_fields = ['name', 'template__name']
    readonly_fields = ['last_run', 'created_date']
    
    fieldsets = (
        ('معلومات الجدولة', {
            'fields': ('name', 'template', 'frequency', 'is_active')
        }),
        ('المعاملات والمستلمون', {
            'fields': ('parameters', 'recipients')
        }),
        ('التوقيت', {
            'fields': ('next_run', 'last_run')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_date'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
