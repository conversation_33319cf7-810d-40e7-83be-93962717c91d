const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();

// Security middleware
app.use(helmet());
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'تم تجاوز الحد المسموح من الطلبات، حاول مرة أخرى لاحقاً'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging
app.use(morgan('combined'));

// Static files
app.use('/uploads', express.static('uploads'));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'نظام ERP Workshop يعمل بنجاح! 🚀',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  });
});

// Welcome endpoint
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'مرحباً بك في نظام ERP Workshop! 🎉',
    version: '1.0.0',
    endpoints: {
      health: '/api/health',
      auth: '/api/auth',
      customers: '/api/customers',
      equipment: '/api/equipment',
      maintenance: '/api/maintenance',
      inventory: '/api/inventory',
      projects: '/api/projects',
      finance: '/api/finance',
      reports: '/api/reports',
      dashboard: '/api/dashboard'
    },
    documentation: 'راجع ملف NODEJS_SETUP.md للتوثيق الكامل'
  });
});

// Simple routes without database
app.get('/api/auth', (req, res) => {
  res.json({
    success: true,
    message: 'Auth routes - قريباً (يحتاج MongoDB)',
    note: 'يرجى تثبيت وتشغيل MongoDB أولاً'
  });
});

app.get('/api/customers', (req, res) => {
  res.json({
    success: true,
    message: 'Customer routes - قريباً (يحتاج MongoDB)',
    note: 'يرجى تثبيت وتشغيل MongoDB أولاً'
  });
});

app.get('/api/equipment', (req, res) => {
  res.json({
    success: true,
    message: 'Equipment routes - قريباً (يحتاج MongoDB)',
    note: 'يرجى تثبيت وتشغيل MongoDB أولاً'
  });
});

app.get('/api/maintenance', (req, res) => {
  res.json({
    success: true,
    message: 'Maintenance routes - قريباً (يحتاج MongoDB)',
    note: 'يرجى تثبيت وتشغيل MongoDB أولاً'
  });
});

app.get('/api/inventory', (req, res) => {
  res.json({
    success: true,
    message: 'Inventory routes - قريباً (يحتاج MongoDB)',
    note: 'يرجى تثبيت وتشغيل MongoDB أولاً'
  });
});

app.get('/api/projects', (req, res) => {
  res.json({
    success: true,
    message: 'Projects routes - قريباً (يحتاج MongoDB)',
    note: 'يرجى تثبيت وتشغيل MongoDB أولاً'
  });
});

app.get('/api/finance', (req, res) => {
  res.json({
    success: true,
    message: 'Finance routes - قريباً (يحتاج MongoDB)',
    note: 'يرجى تثبيت وتشغيل MongoDB أولاً'
  });
});

app.get('/api/reports', (req, res) => {
  res.json({
    success: true,
    message: 'Reports routes - قريباً (يحتاج MongoDB)',
    note: 'يرجى تثبيت وتشغيل MongoDB أولاً'
  });
});

app.get('/api/dashboard', (req, res) => {
  res.json({
    success: true,
    message: 'Dashboard - مرحباً بك في نظام ERP Workshop',
    data: {
      totalUsers: 0,
      totalCustomers: 0,
      totalEquipment: 0,
      totalMaintenanceOrders: 0,
      systemStatus: 'نشط (بدون قاعدة بيانات)',
      lastUpdate: new Date().toISOString(),
      note: 'يرجى تثبيت وتشغيل MongoDB للحصول على البيانات الحقيقية'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود',
    availableEndpoints: [
      '/api',
      '/api/health',
      '/api/dashboard'
    ]
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  
  res.status(500).json({
    success: false,
    message: 'خطأ في الخادم',
    ...(process.env.NODE_ENV === 'development' && { error: err.message })
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log('🎉 ========================================');
  console.log('🚀    ERP Workshop Server Started!');
  console.log('🎉 ========================================');
  console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Server URL: http://localhost:${PORT}`);
  console.log(`🔍 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`📊 Dashboard: http://localhost:${PORT}/api/dashboard`);
  console.log('');
  console.log('⚠️  ملاحظة: MongoDB غير متصل - النظام يعمل في وضع العرض فقط');
  console.log('📚 للحصول على الوظائف الكاملة، يرجى تثبيت وتشغيل MongoDB');
  console.log('');
  console.log('🛑 لإيقاف الخادم: اضغط Ctrl+C');
  console.log('🎉 ========================================');
});
