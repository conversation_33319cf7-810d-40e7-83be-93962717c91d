from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum, Count
from django.utils import timezone
from .models import Invoice, Payment
from .serializers import (
    InvoiceSerializer, 
    InvoiceListSerializer,
    PaymentSerializer
)


class InvoiceViewSet(viewsets.ModelViewSet):
    queryset = Invoice.objects.all()
    serializer_class = InvoiceSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer']
    search_fields = ['invoice_number', 'customer__name', 'maintenance_order__order_number']
    ordering_fields = ['issue_date', 'due_date', 'total_amount']
    ordering = ['-issue_date']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return InvoiceListSerializer
        return InvoiceSerializer
    
    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue invoices"""
        today = timezone.now().date()
        invoices = Invoice.objects.filter(
            due_date__lt=today,
            status__in=['sent', 'draft']
        )
        serializer = InvoiceListSerializer(invoices, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def unpaid(self, request):
        """Get unpaid invoices"""
        invoices = Invoice.objects.filter(status__in=['sent', 'draft'])
        serializer = InvoiceListSerializer(invoices, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get financial statistics"""
        total_invoices = Invoice.objects.count()
        total_amount = Invoice.objects.aggregate(total=Sum('total_amount'))['total'] or 0
        total_paid = Payment.objects.aggregate(total=Sum('amount'))['total'] or 0
        
        by_status = {}
        for status_code, status_name in Invoice.STATUS_CHOICES:
            count = Invoice.objects.filter(status=status_code).count()
            amount = Invoice.objects.filter(status=status_code).aggregate(
                total=Sum('total_amount')
            )['total'] or 0
            by_status[status_code] = {
                'name': status_name, 
                'count': count,
                'amount': amount
            }
        
        today = timezone.now().date()
        overdue_count = Invoice.objects.filter(
            due_date__lt=today,
            status__in=['sent', 'draft']
        ).count()
        
        return Response({
            'total_invoices': total_invoices,
            'total_amount': total_amount,
            'total_paid': total_paid,
            'outstanding_amount': total_amount - total_paid,
            'by_status': by_status,
            'overdue_count': overdue_count
        })
    
    @action(detail=True, methods=['get'])
    def payments(self, request, pk=None):
        """Get payments for a specific invoice"""
        invoice = self.get_object()
        payments = invoice.payment_set.all()
        serializer = PaymentSerializer(payments, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mark_paid(self, request, pk=None):
        """Mark invoice as paid"""
        invoice = self.get_object()
        if invoice.status == 'paid':
            return Response({'error': 'الفاتورة مدفوعة بالفعل'}, status=status.HTTP_400_BAD_REQUEST)
        
        total_paid = sum(payment.amount for payment in invoice.payment_set.all())
        if total_paid >= invoice.total_amount:
            invoice.status = 'paid'
            invoice.save()
            
            serializer = self.get_serializer(invoice)
            return Response(serializer.data)
        else:
            return Response({
                'error': f'المبلغ المدفوع ({total_paid}) أقل من إجمالي الفاتورة ({invoice.total_amount})'
            }, status=status.HTTP_400_BAD_REQUEST)


class PaymentViewSet(viewsets.ModelViewSet):
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['payment_method', 'invoice']
    search_fields = ['invoice__invoice_number', 'reference_number']
    ordering_fields = ['payment_date', 'amount']
    ordering = ['-payment_date']
    
    def perform_create(self, serializer):
        payment = serializer.save()
        
        # Check if invoice should be marked as paid
        invoice = payment.invoice
        total_paid = sum(p.amount for p in invoice.payment_set.all())
        
        if total_paid >= invoice.total_amount and invoice.status != 'paid':
            invoice.status = 'paid'
            invoice.save()
