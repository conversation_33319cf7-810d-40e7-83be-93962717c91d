from rest_framework import viewsets, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import Customer
from .serializers import CustomerSerializer, CustomerListSerializer


class CustomerViewSet(viewsets.ModelViewSet):
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['customer_type']
    search_fields = ['name', 'contact_person', 'phone', 'email']
    ordering_fields = ['name', 'created_date', 'credit_limit']
    ordering = ['-created_date']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return CustomerListSerializer
        return CustomerSerializer
    
    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """Get customers grouped by type"""
        customer_types = Customer.CUSTOMER_TYPES
        result = {}
        
        for type_code, type_name in customer_types:
            customers = Customer.objects.filter(customer_type=type_code)
            result[type_code] = {
                'name': type_name,
                'count': customers.count(),
                'customers': CustomerListSerializer(customers, many=True).data
            }
        
        return Response(result)
    
    @action(detail=True, methods=['get'])
    def equipment(self, request, pk=None):
        """Get all equipment for this customer"""
        customer = self.get_object()
        from equipment.models import Equipment
        from equipment.serializers import EquipmentListSerializer
        
        equipment = Equipment.objects.filter(owner=customer)
        serializer = EquipmentListSerializer(equipment, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def maintenance_orders(self, request, pk=None):
        """Get all maintenance orders for this customer"""
        customer = self.get_object()
        from maintenance.models import MaintenanceOrder
        from maintenance.serializers import MaintenanceOrderListSerializer
        
        orders = MaintenanceOrder.objects.filter(customer=customer)
        serializer = MaintenanceOrderListSerializer(orders, many=True)
        return Response(serializer.data)
