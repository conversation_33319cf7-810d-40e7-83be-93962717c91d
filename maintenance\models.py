from django.db import models
from django.contrib.auth.models import User
from customers.models import Customer

class MaintenanceType(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()
    estimated_hours = models.DecimalField(max_digits=5, decimal_places=2)

class MaintenanceOrder(models.Model):
    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'منخفض'),
        ('medium', 'متوسط'),
        ('high', 'عالي'),
        ('urgent', 'عاجل'),
    ]
    
    order_number = models.CharField(max_length=20, unique=True)
    equipment = models.ForeignKey('equipment.Equipment', on_delete=models.CASCADE)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    maintenance_type = models.ForeignKey(MaintenanceType, on_delete=models.CASCADE)
    description = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    created_date = models.DateTimeField(auto_now_add=True)
    scheduled_date = models.DateTimeField()
    completed_date = models.DateTimeField(null=True, blank=True)
    assigned_technician = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2)
    actual_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)