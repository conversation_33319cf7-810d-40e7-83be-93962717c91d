from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Avg
from .models import Project, ProjectTask
from .serializers import (
    ProjectSerializer, 
    ProjectListSerializer,
    ProjectTaskSerializer
)


class ProjectViewSet(viewsets.ModelViewSet):
    queryset = Project.objects.all()
    serializer_class = ProjectSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer', 'project_manager']
    search_fields = ['name', 'description', 'customer__name']
    ordering_fields = ['name', 'start_date', 'end_date', 'budget']
    ordering = ['-start_date']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return ProjectListSerializer
        return ProjectSerializer
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active projects"""
        projects = Project.objects.filter(status='active')
        serializer = ProjectListSerializer(projects, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def completed(self, request):
        """Get completed projects"""
        projects = Project.objects.filter(status='completed')
        serializer = ProjectListSerializer(projects, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get project statistics"""
        total = Project.objects.count()
        by_status = {}
        
        for status_code, status_name in Project.STATUS_CHOICES:
            count = Project.objects.filter(status=status_code).count()
            by_status[status_code] = {'name': status_name, 'count': count}
        
        # Calculate average budget and actual cost
        avg_budget = Project.objects.aggregate(avg=Avg('budget'))['avg'] or 0
        avg_actual_cost = Project.objects.aggregate(avg=Avg('actual_cost'))['avg'] or 0
        
        return Response({
            'total': total,
            'by_status': by_status,
            'average_budget': avg_budget,
            'average_actual_cost': avg_actual_cost
        })
    
    @action(detail=True, methods=['get'])
    def tasks(self, request, pk=None):
        """Get tasks for a specific project"""
        project = self.get_object()
        tasks = project.tasks.all()
        serializer = ProjectTaskSerializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Mark project as completed"""
        project = self.get_object()
        if project.status == 'completed':
            return Response({'error': 'المشروع مكتمل بالفعل'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Check if all tasks are completed
        incomplete_tasks = project.tasks.filter(completed=False).count()
        if incomplete_tasks > 0:
            return Response({
                'error': f'لا يمكن إكمال المشروع. يوجد {incomplete_tasks} مهام غير مكتملة'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        project.status = 'completed'
        project.save()
        
        serializer = self.get_serializer(project)
        return Response(serializer.data)


class ProjectTaskViewSet(viewsets.ModelViewSet):
    queryset = ProjectTask.objects.all()
    serializer_class = ProjectTaskSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['project', 'assigned_to', 'completed']
    search_fields = ['name', 'description', 'project__name']
    ordering_fields = ['start_date', 'due_date']
    ordering = ['due_date']
    
    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue tasks"""
        from django.utils import timezone
        today = timezone.now().date()
        tasks = ProjectTask.objects.filter(
            due_date__lt=today,
            completed=False
        )
        serializer = ProjectTaskSerializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Mark task as completed"""
        task = self.get_object()
        task.completed = True
        task.save()
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
