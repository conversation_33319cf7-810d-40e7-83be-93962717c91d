# دليل التثبيت - نظام إدارة الموارد للورش

## متطلبات النظام

- Python 3.8 أو أحدث
- PostgreSQL 12 أو أحدث (اختياري - يمكن استخدام SQLite للتطوير)
- Redis (للمهام الخلفية - اختياري)

## خطوات التثبيت

### 1. التثبيت السريع (Windows)

```bash
# تشغيل ملف الإعداد التلقائي
setup.bat
```

### 2. التثبيت اليدوي

#### أ. إنشاء البيئة الافتراضية
```bash
py -m venv venv
venv\Scripts\activate
```

#### ب. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### ج. إعد<PERSON> قاعدة البيانات
```bash
# نسخ ملف الإعدادات
copy .env.example .env

# تحرير ملف .env وإضافة بيانات قاعدة البيانات
# للتطوير يمكن ترك الإعدادات الافتراضية (SQLite)
```

#### د. تشغيل الهجرات
```bash
py manage.py makemigrations
py manage.py migrate
```

#### هـ. إنشاء مستخدم إداري
```bash
py manage.py createsuperuser
```

#### و. تشغيل الخادم
```bash
py manage.py runserver
```

## إعداد قاعدة البيانات

### استخدام SQLite (للتطوير)
لا حاجة لإعداد إضافي - Django سيستخدم SQLite تلقائياً.

### استخدام PostgreSQL (للإنتاج)

1. **تثبيت PostgreSQL**
2. **إنشاء قاعدة بيانات**
```sql
CREATE DATABASE erp_workshop;
CREATE USER erp_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE erp_workshop TO erp_user;
```

3. **تحديث ملف .env**
```env
DB_NAME=erp_workshop
DB_USER=erp_user
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=5432
```

## إعداد Redis (اختياري)

لتشغيل المهام الخلفية مثل إنشاء التقارير:

1. **تثبيت Redis**
2. **تشغيل Redis server**
3. **تشغيل Celery worker**
```bash
celery -A erp_workshop worker -l info
```

## اختبار التثبيت

1. **فتح المتصفح والذهاب إلى:**
   - `http://localhost:8000/admin/` - لوحة الإدارة
   - `http://localhost:8000/api/` - واجهة API

2. **تسجيل الدخول باستخدام المستخدم الإداري المُنشأ**

## حل المشاكل الشائعة

### خطأ "ModuleNotFoundError: No module named 'django'"
```bash
# تأكد من تفعيل البيئة الافتراضية
venv\Scripts\activate
pip install -r requirements.txt
```

### خطأ قاعدة البيانات
```bash
# تأكد من إعدادات قاعدة البيانات في .env
# أو استخدم SQLite للتطوير (احذف إعدادات DB من .env)
```

### خطأ الهجرات
```bash
# حذف ملفات الهجرات وإعادة إنشائها
py manage.py makemigrations --empty customers
py manage.py makemigrations
py manage.py migrate
```

## الخطوات التالية

بعد التثبيت الناجح:

1. **استكشاف لوحة الإدارة** - إضافة البيانات الأساسية
2. **قراءة دليل المستخدم** - فهم وظائف النظام
3. **إعداد النسخ الاحتياطي** - حماية البيانات
4. **تخصيص النظام** - حسب احتياجاتك

## الدعم

للحصول على المساعدة:
- راجع ملف README.md
- تحقق من Issues في GitHub
- اتصل بفريق الدعم
