from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db import models
from .models import SparePart, StockMovement
from .serializers import (
    SparePartSerializer, 
    SparePartListSerializer,
    StockMovementSerializer
)


class SparePartViewSet(viewsets.ModelViewSet):
    queryset = SparePart.objects.all()
    serializer_class = SparePartSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'supplier']
    search_fields = ['part_number', 'name', 'description']
    ordering_fields = ['name', 'quantity_in_stock', 'unit_price']
    ordering = ['name']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return SparePartListSerializer
        return SparePartSerializer
    
    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """Get spare parts with low stock"""
        parts = SparePart.objects.filter(quantity_in_stock__lte=models.F('minimum_stock_level'))
        serializer = SparePartListSerializer(parts, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def out_of_stock(self, request):
        """Get spare parts that are out of stock"""
        parts = SparePart.objects.filter(quantity_in_stock=0)
        serializer = SparePartListSerializer(parts, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get inventory statistics"""
        from django.db.models import Sum, Avg, Count
        
        total_parts = SparePart.objects.count()
        total_value = SparePart.objects.aggregate(
            total=Sum(models.F('quantity_in_stock') * models.F('unit_price'))
        )['total'] or 0
        
        low_stock_count = SparePart.objects.filter(
            quantity_in_stock__lte=models.F('minimum_stock_level')
        ).count()
        
        out_of_stock_count = SparePart.objects.filter(quantity_in_stock=0).count()
        
        categories = SparePart.objects.values('category').annotate(
            count=Count('id')
        ).order_by('-count')
        
        return Response({
            'total_parts': total_parts,
            'total_value': total_value,
            'low_stock_count': low_stock_count,
            'out_of_stock_count': out_of_stock_count,
            'categories': list(categories)
        })
    
    @action(detail=True, methods=['get'])
    def movements(self, request, pk=None):
        """Get stock movements for a specific spare part"""
        spare_part = self.get_object()
        movements = StockMovement.objects.filter(spare_part=spare_part).order_by('-date')
        serializer = StockMovementSerializer(movements, many=True)
        return Response(serializer.data)


class StockMovementViewSet(viewsets.ModelViewSet):
    queryset = StockMovement.objects.all()
    serializer_class = StockMovementSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['movement_type', 'spare_part']
    search_fields = ['spare_part__name', 'spare_part__part_number', 'reference']
    ordering_fields = ['date']
    ordering = ['-date']
    
    @action(detail=False, methods=['get'])
    def recent(self, request):
        """Get recent stock movements"""
        movements = StockMovement.objects.all()[:20]
        serializer = StockMovementSerializer(movements, many=True)
        return Response(serializer.data)
