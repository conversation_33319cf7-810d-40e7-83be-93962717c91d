from rest_framework import serializers
from .models import Customer


class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = '__all__'
        read_only_fields = ['created_date']
    
    def validate_credit_limit(self, value):
        if value < 0:
            raise serializers.ValidationError("حد الائتمان لا يمكن أن يكون سالباً")
        return value


class CustomerListSerializer(serializers.ModelSerializer):
    """Serializer for listing customers with minimal fields"""
    class Meta:
        model = Customer
        fields = ['id', 'name', 'customer_type', 'contact_person', 'phone', 'email']
