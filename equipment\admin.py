from django.contrib import admin
from .models import EquipmentCategory, Equipment, EquipmentDocument


@admin.register(EquipmentCategory)
class EquipmentCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name']


class EquipmentDocumentInline(admin.TabularInline):
    model = EquipmentDocument
    extra = 1


@admin.register(Equipment)
class EquipmentAdmin(admin.ModelAdmin):
    list_display = ['equipment_id', 'name', 'category', 'manufacturer', 'status', 'condition', 'owner', 'last_maintenance']
    list_filter = ['status', 'condition', 'category', 'manufacturer', 'created_date']
    search_fields = ['equipment_id', 'name', 'serial_number', 'model']
    readonly_fields = ['created_date', 'updated_date', 'is_under_warranty', 'maintenance_due']
    inlines = [EquipmentDocumentInline]
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('equipment_id', 'name', 'category', 'model', 'manufacturer', 'serial_number')
        }),
        ('معلومات الشراء', {
            'fields': ('purchase_date', 'purchase_price', 'current_value', 'warranty_expiry')
        }),
        ('الموقع والحالة', {
            'fields': ('location', 'status', 'condition', 'owner')
        }),
        ('الصيانة', {
            'fields': ('last_maintenance', 'next_maintenance')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('is_under_warranty', 'maintenance_due', 'created_date', 'updated_date'),
            'classes': ('collapse',)
        }),
    )


@admin.register(EquipmentDocument)
class EquipmentDocumentAdmin(admin.ModelAdmin):
    list_display = ['equipment', 'document_type', 'title', 'uploaded_date']
    list_filter = ['document_type', 'uploaded_date']
    search_fields = ['equipment__name', 'title']
