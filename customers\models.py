from django.db import models

class Customer(models.Model):
    CUSTOMER_TYPES = [
        ('individual', 'فرد'),
        ('company', 'شركة'),
        ('government', 'جهة حكومية'),
    ]
    
    name = models.CharField(max_length=200)
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPES)
    contact_person = models.CharField(max_length=100)
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    address = models.TextField()
    tax_number = models.CharField(max_length=50, blank=True)
    credit_limit = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    created_date = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name