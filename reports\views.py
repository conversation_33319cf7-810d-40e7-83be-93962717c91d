from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import ReportTemplate, GeneratedReport, ReportSchedule
from .serializers import (
    ReportTemplateSerializer,
    GeneratedReportSerializer,
    ReportScheduleSerializer
)


class ReportTemplateViewSet(viewsets.ModelViewSet):
    queryset = ReportTemplate.objects.all()
    serializer_class = ReportTemplateSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['report_type', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_date']
    ordering = ['name']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """Get templates grouped by type"""
        result = {}
        for type_code, type_name in ReportTemplate.REPORT_TYPES:
            templates = ReportTemplate.objects.filter(
                report_type=type_code, 
                is_active=True
            )
            result[type_code] = {
                'name': type_name,
                'templates': ReportTemplateSerializer(templates, many=True).data
            }
        return Response(result)


class GeneratedReportViewSet(viewsets.ModelViewSet):
    queryset = GeneratedReport.objects.all()
    serializer_class = GeneratedReportSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'template', 'generated_by']
    search_fields = ['title', 'template__name']
    ordering_fields = ['generated_date', 'completed_date']
    ordering = ['-generated_date']
    
    def perform_create(self, serializer):
        serializer.save(generated_by=self.request.user)
    
    @action(detail=False, methods=['get'])
    def recent(self, request):
        """Get recent generated reports"""
        reports = GeneratedReport.objects.all()[:10]
        serializer = GeneratedReportSerializer(reports, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def failed(self, request):
        """Get failed reports"""
        reports = GeneratedReport.objects.filter(status='failed')
        serializer = GeneratedReportSerializer(reports, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def regenerate(self, request, pk=None):
        """Regenerate a failed report"""
        report = self.get_object()
        if report.status != 'failed':
            return Response({
                'error': 'يمكن إعادة إنشاء التقارير الفاشلة فقط'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Reset report status
        report.status = 'pending'
        report.error_message = ''
        report.save()
        
        # Here you would trigger the report generation task
        # For now, we'll just return the updated report
        serializer = self.get_serializer(report)
        return Response(serializer.data)


class ReportScheduleViewSet(viewsets.ModelViewSet):
    queryset = ReportSchedule.objects.all()
    serializer_class = ReportScheduleSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['frequency', 'is_active', 'template']
    search_fields = ['name', 'template__name']
    ordering_fields = ['name', 'next_run']
    ordering = ['next_run']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active schedules"""
        schedules = ReportSchedule.objects.filter(is_active=True)
        serializer = ReportScheduleSerializer(schedules, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def due(self, request):
        """Get schedules that are due to run"""
        from django.utils import timezone
        now = timezone.now()
        schedules = ReportSchedule.objects.filter(
            is_active=True,
            next_run__lte=now
        )
        serializer = ReportScheduleSerializer(schedules, many=True)
        return Response(serializer.data)
