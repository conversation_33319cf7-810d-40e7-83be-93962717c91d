from django.contrib import admin
from .models import Project, ProjectTask


class ProjectTaskInline(admin.TabularInline):
    model = ProjectTask
    extra = 1
    fields = ['name', 'assigned_to', 'start_date', 'due_date', 'completed']


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'customer', 'status', 'project_manager', 'start_date', 'end_date', 'budget', 'actual_cost']
    list_filter = ['status', 'start_date', 'end_date']
    search_fields = ['name', 'customer__name', 'description']
    inlines = [ProjectTaskInline]
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'description', 'customer', 'project_manager')
        }),
        ('التواريخ', {
            'fields': ('start_date', 'end_date')
        }),
        ('الحالة والميزانية', {
            'fields': ('status', 'budget', 'actual_cost')
        }),
    )
    
    def get_readonly_fields(self, request, obj=None):
        if obj and obj.status == 'completed':
            return ['customer', 'start_date', 'budget']
        return []


@admin.register(ProjectTask)
class ProjectTaskAdmin(admin.ModelAdmin):
    list_display = ['name', 'project', 'assigned_to', 'start_date', 'due_date', 'completed']
    list_filter = ['completed', 'project', 'assigned_to', 'start_date']
    search_fields = ['name', 'project__name', 'description']
    
    fieldsets = (
        ('معلومات المهمة', {
            'fields': ('project', 'name', 'description')
        }),
        ('التعيين والتواريخ', {
            'fields': ('assigned_to', 'start_date', 'due_date', 'completed')
        }),
        ('الصيانة المرتبطة', {
            'fields': ('maintenance_order',)
        }),
    )
