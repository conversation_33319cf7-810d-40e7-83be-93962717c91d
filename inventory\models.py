from django.db import models

class SparePart(models.Model):
    part_number = models.CharField(max_length=50, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField()
    category = models.CharField(max_length=100)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    quantity_in_stock = models.IntegerField(default=0)
    minimum_stock_level = models.IntegerField(default=5)
    supplier = models.CharField(max_length=200)
    
    @property
    def is_low_stock(self):
        return self.quantity_in_stock <= self.minimum_stock_level

class StockMovement(models.Model):
    MOVEMENT_TYPES = [
        ('in', 'وارد'),
        ('out', 'صادر'),
        ('adjustment', 'تعديل'),
    ]
    
    spare_part = models.ForeignKey(SparePart, on_delete=models.CASCADE)
    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPES)
    quantity = models.IntegerField()
    reference = models.CharField(max_length=100)  # رقم أمر الصيانة أو الفاتورة
    date = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)