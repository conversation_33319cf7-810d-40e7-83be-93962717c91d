# نظام إدارة الموارد للورش (ERP Workshop)

نظام شامل لإدارة الموارد في الورش والمصانع، يشمل إدارة المعدات والصيانة والمخزون والعملاء والمشاريع والمالية.

## المميزات

### 🔧 إدارة المعدات
- تسجيل وتتبع جميع المعدات
- إدارة فئات المعدات
- تتبع حالة المعدات والصيانة
- إدارة مستندات المعدات

### 🛠️ إدارة الصيانة
- إنشاء أوامر الصيانة
- تتبع حالة الصيانة
- إدارة أنواع الصيانة
- تعيين الفنيين

### 📦 إدارة المخزون
- إدارة قطع الغيار
- تتبع حركات المخزون
- تنبيهات المخزون المنخفض
- إدارة الموردين

### 👥 إدارة العملاء
- قاعدة بيانات العملاء
- تصنيف العملاء
- تتبع معلومات الاتصال

### 📋 إدارة المشاريع
- إنشاء وتتبع المشاريع
- إدارة مهام المشاريع
- تتبع التقدم والميزانية

### 💰 إدارة المالية
- إنشاء الفواتير
- تتبع المدفوعات
- التقارير المالية

### 📊 التقارير
- قوالب التقارير المخصصة
- جدولة التقارير التلقائية
- تصدير التقارير

## متطلبات النظام

- Python 3.8+
- PostgreSQL 12+
- Redis (للمهام الخلفية)

## التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd ERP_Fady
```

2. **إنشاء البيئة الافتراضية**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد قاعدة البيانات**
```bash
# إنشاء ملف .env من المثال
cp .env.example .env
# تحرير .env وإضافة بيانات قاعدة البيانات

# تشغيل الهجرات
python manage.py makemigrations
python manage.py migrate
```

5. **إنشاء مستخدم إداري**
```bash
python manage.py createsuperuser
```

6. **تشغيل الخادم**
```bash
python manage.py runserver
```

## استخدام API

النظام يوفر REST API شامل لجميع الوظائف:

### نقاط النهاية الرئيسية

- `/api/customers/` - إدارة العملاء
- `/api/equipment/` - إدارة المعدات
- `/api/maintenance/` - إدارة الصيانة
- `/api/inventory/` - إدارة المخزون
- `/api/projects/` - إدارة المشاريع
- `/api/finance/` - إدارة المالية
- `/api/reports/` - إدارة التقارير

### مثال على الاستخدام

```python
import requests

# الحصول على قائمة العملاء
response = requests.get('http://localhost:8000/api/customers/')
customers = response.json()

# إنشاء عميل جديد
new_customer = {
    'name': 'شركة المثال',
    'customer_type': 'company',
    'contact_person': 'أحمد محمد',
    'phone': '0501234567',
    'email': '<EMAIL>',
    'address': 'الرياض، المملكة العربية السعودية'
}
response = requests.post('http://localhost:8000/api/customers/', json=new_customer)
```

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم، يرجى فتح issue في GitHub أو التواصل عبر البريد الإلكتروني.
