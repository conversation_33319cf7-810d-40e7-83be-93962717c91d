from rest_framework import serializers
from .models import Invoice, Payment
from customers.serializers import CustomerListSerializer


class PaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = '__all__'
    
    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("مبلغ الدفع يجب أن يكون أكبر من صفر")
        return value


class InvoiceSerializer(serializers.ModelSerializer):
    customer_details = CustomerListSerializer(source='customer', read_only=True)
    maintenance_order_number = serializers.CharField(source='maintenance_order.order_number', read_only=True)
    payments = PaymentSerializer(many=True, read_only=True)
    total_paid = serializers.SerializerMethodField()
    remaining_amount = serializers.SerializerMethodField()
    
    class Meta:
        model = Invoice
        fields = '__all__'
        read_only_fields = ['issue_date']
    
    def get_total_paid(self, obj):
        return sum(payment.amount for payment in obj.payment_set.all())
    
    def get_remaining_amount(self, obj):
        total_paid = self.get_total_paid(obj)
        return obj.total_amount - total_paid
    
    def validate_subtotal(self, value):
        if value <= 0:
            raise serializers.ValidationError("المبلغ الفرعي يجب أن يكون أكبر من صفر")
        return value
    
    def validate_tax_amount(self, value):
        if value < 0:
            raise serializers.ValidationError("مبلغ الضريبة لا يمكن أن يكون سالباً")
        return value
    
    def validate_total_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("المبلغ الإجمالي يجب أن يكون أكبر من صفر")
        return value
    
    def validate(self, data):
        subtotal = data.get('subtotal')
        tax_amount = data.get('tax_amount')
        total_amount = data.get('total_amount')
        
        if subtotal and tax_amount and total_amount:
            expected_total = subtotal + tax_amount
            if abs(total_amount - expected_total) > 0.01:  # Allow for small rounding differences
                raise serializers.ValidationError("المبلغ الإجمالي لا يتطابق مع مجموع المبلغ الفرعي والضريبة")
        
        return data


class InvoiceListSerializer(serializers.ModelSerializer):
    """Serializer for listing invoices with minimal fields"""
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    maintenance_order_number = serializers.CharField(source='maintenance_order.order_number', read_only=True)
    total_paid = serializers.SerializerMethodField()
    remaining_amount = serializers.SerializerMethodField()
    
    class Meta:
        model = Invoice
        fields = ['id', 'invoice_number', 'customer_name', 'maintenance_order_number',
                 'issue_date', 'due_date', 'total_amount', 'total_paid', 'remaining_amount', 'status']
    
    def get_total_paid(self, obj):
        return sum(payment.amount for payment in obj.payment_set.all())
    
    def get_remaining_amount(self, obj):
        total_paid = self.get_total_paid(obj)
        return obj.total_amount - total_paid
